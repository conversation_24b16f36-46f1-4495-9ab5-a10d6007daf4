{% extends 'base.html' %}

{% block title %}Dashboard - Crypto Portfolio Tracker{% endblock %}

{% block extra_css %}
<style>
    .crypto-card {
        transition: transform 0.2s;
    }
    .crypto-card:hover {
        transform: translateY(-2px);
    }
    .price-positive {
        color: #28a745;
    }
    .price-negative {
        color: #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1><i class="fas fa-tachometer-alt"></i> Dashboard</h1>
        <p class="text-muted">Welcome back, {{ user.get_full_name|default:user.username }}!</p>
    </div>
</div>

<!-- Portfolio Overview Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>${{ total_portfolio_value|floatformat:2|default:"0.00" }}</h4>
                        <p class="mb-0">Total Portfolio Value</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-wallet fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-{% if total_gain_loss >= 0 %}success{% else %}danger{% endif %} text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{% if total_gain_loss >= 0 %}+{% endif %}${{ total_gain_loss|floatformat:2|default:"0.00" }}</h4>
                        <p class="mb-0">Total P&L</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ total_holdings }}</h4>
                        <p class="mb-0">Total Holdings</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-coins fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ active_alerts }}</h4>
                        <p class="mb-0">Active Alerts</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-bell fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Portfolio Chart -->
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie"></i> Portfolio Distribution</h5>
            </div>
            <div class="card-body">
                <canvas id="portfolioChart" width="400" height="200"></canvas>
            </div>
        </div>
        
        <!-- Recent Transactions -->
        <div class="card shadow mt-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-exchange-alt"></i> Recent Transactions</h5>
                <a href="{% url 'portfolio:transaction_list' %}" class="btn btn-sm btn-outline-primary">
                    View All
                </a>
            </div>
            <div class="card-body">
                {% if recent_transactions %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Type</th>
                                    <th>Crypto</th>
                                    <th>Amount</th>
                                    <th>Price</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in recent_transactions %}
                                    <tr>
                                        <td>{{ transaction.created_at|date:"M d, Y" }}</td>
                                        <td>
                                            <span class="badge bg-{% if transaction.transaction_type == 'buy' %}success{% else %}danger{% endif %}">
                                                {{ transaction.get_transaction_type_display }}
                                            </span>
                                        </td>
                                        <td>{{ transaction.cryptocurrency.symbol|upper }}</td>
                                        <td>{{ transaction.amount|floatformat:8 }}</td>
                                        <td>${{ transaction.price_per_unit|floatformat:2 }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                        <h5>No Transactions Yet</h5>
                        <p class="text-muted">Start by adding your first cryptocurrency transaction.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Top Holdings & Market Data -->
    <div class="col-md-4">
        <!-- Top Holdings -->
        <div class="card shadow">
            <div class="card-header">
                <h5><i class="fas fa-star"></i> Top Holdings</h5>
            </div>
            <div class="card-body">
                {% if top_holdings %}
                    {% for holding in top_holdings %}
                        <div class="d-flex justify-content-between align-items-center mb-3 crypto-card p-2 border rounded">
                            <div>
                                <h6 class="mb-0">{{ holding.cryptocurrency.name }}</h6>
                                <small class="text-muted">{{ holding.cryptocurrency.symbol|upper }}</small>
                            </div>
                            <div class="text-end">
                                <div class="fw-bold">${{ holding.current_value|floatformat:2 }}</div>
                                <small class="{% if holding.gain_loss >= 0 %}price-positive{% else %}price-negative{% endif %}">
                                    {% if holding.gain_loss >= 0 %}+{% endif %}{{ holding.gain_loss_percentage|floatformat:1 }}%
                                </small>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-coins fa-3x text-muted mb-3"></i>
                        <h6>No Holdings Yet</h6>
                        <p class="text-muted small">Add cryptocurrencies to your portfolio to see them here.</p>
                    </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Price Alerts -->
        <div class="card shadow mt-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-bell"></i> Price Alerts</h5>
                <a href="{% url 'portfolio:alert_create' %}" class="btn btn-sm btn-outline-success">
                    <i class="fas fa-plus"></i>
                </a>
            </div>
            <div class="card-body">
                {% if recent_alerts %}
                    {% for alert in recent_alerts %}
                        <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                            <div>
                                <small class="fw-bold">{{ alert.cryptocurrency.symbol|upper }}</small>
                                <br>
                                <small class="text-muted">
                                    {{ alert.get_alert_type_display }} ${{ alert.target_price|floatformat:2 }}
                                </small>
                            </div>
                            <div>
                                <span class="badge bg-{% if alert.is_active %}success{% else %}secondary{% endif %}">
                                    {% if alert.is_active %}Active{% else %}Inactive{% endif %}
                                </span>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-bell-slash fa-2x text-muted mb-2"></i>
                        <p class="text-muted small mb-0">No active alerts</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Portfolio Distribution Chart
const ctx = document.getElementById('portfolioChart').getContext('2d');
const portfolioChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: {{ portfolio_labels|safe }},
        datasets: [{
            data: {{ portfolio_data|safe }},
            backgroundColor: [
                '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', 
                '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Auto-refresh data every 30 seconds
setInterval(function() {
    location.reload();
}, 30000);
</script>
{% endblock %}
