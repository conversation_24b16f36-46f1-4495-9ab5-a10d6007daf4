{% extends 'base.html' %}

{% block title %}{{ portfolio.name }} - Crypto Portfolio Tracker{% endblock %}

{% block extra_css %}
<style>
    .holding-card {
        transition: transform 0.2s;
    }
    .holding-card:hover {
        transform: translateY(-2px);
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1><i class="fas fa-briefcase"></i> {{ portfolio.name }}</h1>
        <p class="text-muted">{{ portfolio.description }}</p>
    </div>
    <div class="col-md-4 text-end">
        <div class="btn-group" role="group">
            <a href="{% url 'portfolio:holding_add' portfolio.id %}" class="btn btn-success">
                <i class="fas fa-plus"></i> Add Holding
            </a>
            <a href="{% url 'portfolio:portfolio_edit' portfolio.id %}" class="btn btn-outline-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <button class="btn btn-outline-danger" onclick="confirmDelete()">
                <i class="fas fa-trash"></i> Delete
            </button>
        </div>
    </div>
</div>

<!-- Portfolio Summary -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <h4>${{ portfolio.total_value|floatformat:2|default:"0.00" }}</h4>
                <p class="mb-0">Total Value</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-{% if portfolio.total_gain_loss >= 0 %}success{% else %}danger{% endif %} text-white">
            <div class="card-body">
                <h4>{% if portfolio.total_gain_loss >= 0 %}+{% endif %}${{ portfolio.total_gain_loss|floatformat:2|default:"0.00" }}</h4>
                <p class="mb-0">Total P&L</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <h4>{{ portfolio.holdings.count }}</h4>
                <p class="mb-0">Holdings</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <h4>{% if portfolio.total_gain_loss_percentage >= 0 %}+{% endif %}{{ portfolio.total_gain_loss_percentage|floatformat:2|default:"0.00" }}%</h4>
                <p class="mb-0">Total Return</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Holdings List -->
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-coins"></i> Holdings</h5>
                <a href="{% url 'portfolio:holding_create' portfolio.id %}" class="btn btn-sm btn-success">
                    <i class="fas fa-plus"></i> Add Holding
                </a>
            </div>
            <div class="card-body">
                {% if portfolio.holdings.all %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Cryptocurrency</th>
                                    <th>Amount</th>
                                    <th>Avg. Price</th>
                                    <th>Current Price</th>
                                    <th>Value</th>
                                    <th>P&L</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for holding in portfolio.holdings.all %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div>
                                                    <div class="fw-bold">{{ holding.cryptocurrency.name }}</div>
                                                    <small class="text-muted">{{ holding.cryptocurrency.symbol|upper }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ holding.amount|floatformat:8 }}</td>
                                        <td>${{ holding.average_price|floatformat:2 }}</td>
                                        <td>${{ holding.cryptocurrency.current_price|floatformat:2 }}</td>
                                        <td class="fw-bold">${{ holding.current_value|floatformat:2 }}</td>
                                        <td class="{% if holding.gain_loss >= 0 %}text-success{% else %}text-danger{% endif %}">
                                            {% if holding.gain_loss >= 0 %}+{% endif %}${{ holding.gain_loss|floatformat:2 }}
                                            <br>
                                            <small>({% if holding.gain_loss_percentage >= 0 %}+{% endif %}{{ holding.gain_loss_percentage|floatformat:1 }}%)</small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{% url 'portfolio:holding_edit' holding.id %}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteHolding({{ holding.id }}, '{{ holding.cryptocurrency.name }}')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-coins fa-3x text-muted mb-3"></i>
                        <h5>No Holdings Yet</h5>
                        <p class="text-muted">Add your first cryptocurrency holding to this portfolio.</p>
                        <a href="{% url 'portfolio:holding_create' portfolio.id %}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add First Holding
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Portfolio Chart & Info -->
    <div class="col-md-4">
        <!-- Portfolio Distribution -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie"></i> Distribution</h5>
            </div>
            <div class="card-body">
                <canvas id="portfolioChart" width="300" height="300"></canvas>
            </div>
        </div>
        
        <!-- Portfolio Info -->
        <div class="card shadow">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> Portfolio Info</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Created:</strong><br>
                    <small class="text-muted">{{ portfolio.created_at|date:"F d, Y" }}</small>
                </div>
                
                <div class="mb-3">
                    <strong>Last Updated:</strong><br>
                    <small class="text-muted">{{ portfolio.updated_at|date:"F d, Y H:i" }}</small>
                </div>
                
                <div class="mb-3">
                    <strong>Total Invested:</strong><br>
                    <span class="text-primary">${{ portfolio.total_invested|floatformat:2|default:"0.00" }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>Best Performer:</strong><br>
                    {% if best_performer %}
                        <span class="text-success">{{ best_performer.cryptocurrency.symbol|upper }}</span>
                        <small class="text-muted">(+{{ best_performer.gain_loss_percentage|floatformat:1 }}%)</small>
                    {% else %}
                        <small class="text-muted">No data</small>
                    {% endif %}
                </div>
                
                <div class="mb-0">
                    <strong>Worst Performer:</strong><br>
                    {% if worst_performer %}
                        <span class="text-danger">{{ worst_performer.cryptocurrency.symbol|upper }}</span>
                        <small class="text-muted">({{ worst_performer.gain_loss_percentage|floatformat:1 }}%)</small>
                    {% else %}
                        <small class="text-muted">No data</small>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this portfolio?</p>
                <p class="text-danger"><strong>This action cannot be undone and will delete all holdings in this portfolio.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <a href="{% url 'portfolio:portfolio_delete' portfolio.id %}" class="btn btn-danger">Delete Portfolio</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Portfolio Distribution Chart
const ctx = document.getElementById('portfolioChart').getContext('2d');
const portfolioChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: {{ holding_labels|safe }},
        datasets: [{
            data: {{ holding_data|safe }},
            backgroundColor: [
                '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', 
                '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

function confirmDelete() {
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function deleteHolding(holdingId, cryptoName) {
    if (confirm(`Are you sure you want to delete the ${cryptoName} holding?`)) {
        // This would typically be an AJAX call
        window.location.href = `/portfolio/holding/${holdingId}/delete/`;
    }
}
</script>
{% endblock %}
