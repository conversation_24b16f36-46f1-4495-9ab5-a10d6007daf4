from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from decimal import Decimal
import uuid

User = get_user_model()


class Cryptocurrency(models.Model):
    """
    Model to store cryptocurrency information
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    coingecko_id = models.CharField(max_length=100, unique=True)
    symbol = models.CharField(max_length=10)
    name = models.CharField(max_length=100)
    image_url = models.URLField(blank=True)
    market_cap_rank = models.IntegerField(null=True, blank=True)
    
    # Current price data
    current_price_usd = models.DecimalField(max_digits=20, decimal_places=8, default=0)
    market_cap = models.BigIntegerField(null=True, blank=True)
    total_volume = models.BigIntegerField(null=True, blank=True)
    
    # Price change data
    price_change_24h = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    price_change_percentage_24h = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    price_change_percentage_7d = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    price_change_percentage_30d = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    last_updated = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = 'Cryptocurrency'
        verbose_name_plural = 'Cryptocurrencies'
        ordering = ['market_cap_rank', 'name']
    
    def __str__(self):
        return f"{self.name} ({self.symbol.upper()})"


class Portfolio(models.Model):
    """
    User's cryptocurrency portfolio
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='portfolios')
    name = models.CharField(max_length=100, default='My Portfolio')
    description = models.TextField(blank=True)
    is_default = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = 'Portfolio'
        verbose_name_plural = 'Portfolios'
        ordering = ['-is_default', '-created_at']
        unique_together = ['user', 'name']
    
    def __str__(self):
        return f"{self.user.get_full_name()}'s {self.name}"
    
    def get_total_value(self):
        """Calculate total portfolio value in USD"""
        total = Decimal('0.00')
        for holding in self.holdings.all():
            total += holding.get_current_value()
        return total
    
    def get_total_cost(self):
        """Calculate total cost basis of portfolio"""
        total = Decimal('0.00')
        for holding in self.holdings.all():
            total += holding.get_total_cost()
        return total
    
    def get_total_gain_loss(self):
        """Calculate total gain/loss"""
        return self.get_total_value() - self.get_total_cost()
    
    def get_total_gain_loss_percentage(self):
        """Calculate total gain/loss percentage"""
        total_cost = self.get_total_cost()
        if total_cost > 0:
            return ((self.get_total_value() - total_cost) / total_cost) * 100
        return Decimal('0.00')


class CryptoHolding(models.Model):
    """
    Individual cryptocurrency holding within a portfolio
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    portfolio = models.ForeignKey(Portfolio, on_delete=models.CASCADE, related_name='holdings')
    cryptocurrency = models.ForeignKey(Cryptocurrency, on_delete=models.CASCADE)
    
    # Holding details
    quantity = models.DecimalField(max_digits=20, decimal_places=8)
    average_cost = models.DecimalField(max_digits=20, decimal_places=8, default=0)
    
    # Metadata
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = 'Crypto Holding'
        verbose_name_plural = 'Crypto Holdings'
        unique_together = ['portfolio', 'cryptocurrency']
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.quantity} {self.cryptocurrency.symbol.upper()} in {self.portfolio.name}"
    
    def get_current_value(self):
        """Calculate current value of holding"""
        return self.quantity * self.cryptocurrency.current_price_usd
    
    def get_total_cost(self):
        """Calculate total cost basis of holding"""
        return self.quantity * self.average_cost
    
    def get_gain_loss(self):
        """Calculate gain/loss for this holding"""
        return self.get_current_value() - self.get_total_cost()
    
    def get_gain_loss_percentage(self):
        """Calculate gain/loss percentage for this holding"""
        total_cost = self.get_total_cost()
        if total_cost > 0:
            return ((self.get_current_value() - total_cost) / total_cost) * 100
        return Decimal('0.00')


class Transaction(models.Model):
    """
    Transaction history for crypto holdings
    """
    TRANSACTION_TYPES = [
        ('buy', 'Buy'),
        ('sell', 'Sell'),
        ('transfer_in', 'Transfer In'),
        ('transfer_out', 'Transfer Out'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    holding = models.ForeignKey(CryptoHolding, on_delete=models.CASCADE, related_name='transactions')
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPES)
    
    # Transaction details
    quantity = models.DecimalField(max_digits=20, decimal_places=8)
    price_per_unit = models.DecimalField(max_digits=20, decimal_places=8)
    total_amount = models.DecimalField(max_digits=20, decimal_places=2)
    fees = models.DecimalField(max_digits=20, decimal_places=2, default=0)
    
    # Metadata
    exchange = models.CharField(max_length=100, blank=True)
    transaction_hash = models.CharField(max_length=255, blank=True)
    notes = models.TextField(blank=True)
    transaction_date = models.DateTimeField(default=timezone.now)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = 'Transaction'
        verbose_name_plural = 'Transactions'
        ordering = ['-transaction_date']
    
    def __str__(self):
        return f"{self.transaction_type.title()} {self.quantity} {self.holding.cryptocurrency.symbol.upper()}"


class PriceAlert(models.Model):
    """
    Price alerts for cryptocurrencies
    """
    ALERT_TYPES = [
        ('above', 'Price Above'),
        ('below', 'Price Below'),
        ('percentage_change', 'Percentage Change'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='price_alerts')
    cryptocurrency = models.ForeignKey(Cryptocurrency, on_delete=models.CASCADE)
    
    alert_type = models.CharField(max_length=20, choices=ALERT_TYPES)
    target_price = models.DecimalField(max_digits=20, decimal_places=8, null=True, blank=True)
    percentage_change = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    
    is_active = models.BooleanField(default=True)
    is_triggered = models.BooleanField(default=False)
    triggered_at = models.DateTimeField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = 'Price Alert'
        verbose_name_plural = 'Price Alerts'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Alert for {self.cryptocurrency.symbol.upper()} - {self.alert_type}"
