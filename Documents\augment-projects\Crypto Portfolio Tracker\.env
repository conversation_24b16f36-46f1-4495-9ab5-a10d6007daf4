# Django Configuration
SECRET_KEY=django-insecure-crypto-portfolio-dev-key-change-in-production
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration - Using SQLite for development
USE_SQLITE=True

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# CoinGecko API Configuration
COINGECKO_API_KEY=

# Email Configuration
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
