from django.shortcuts import get_object_or_404
from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db import models
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from datetime import timedel<PERSON>
from .models import PriceHistory, APICallLog, MarketData, NewsArticle
from portfolio.models import Cryptocurrency


class MarketOverviewView(LoginRequiredMixin, TemplateView):
    """
    Market overview page showing cryptocurrency market data
    """
    template_name = 'crypto_api/market_overview.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get top cryptocurrencies by market cap
        top_cryptos = Cryptocurrency.objects.filter(
            market_cap_rank__lte=100
        ).order_by('market_cap_rank')[:50]

        # Calculate market statistics
        total_market_cap = sum(crypto.market_cap or 0 for crypto in top_cryptos)
        total_volume = sum(crypto.total_volume or 0 for crypto in top_cryptos)

        # Get Bitcoin dominance (if Bitcoin exists)
        btc_dominance = 0
        try:
            bitcoin = Cryptocurrency.objects.get(symbol__iexact='btc')
            if bitcoin.market_cap and total_market_cap > 0:
                btc_dominance = (bitcoin.market_cap / total_market_cap) * 100
        except Cryptocurrency.DoesNotExist:
            pass

        # Convert to billions for display
        global_market_cap = total_market_cap / 1_000_000_000 if total_market_cap else 0
        global_volume = total_volume / 1_000_000_000 if total_volume else 0

        context.update({
            'cryptocurrencies': top_cryptos,
            'global_market_cap': global_market_cap,
            'global_volume': global_volume,
            'btc_dominance': btc_dominance,
            'active_cryptos': top_cryptos.count(),
        })

        return context


class CryptocurrencyListAPIView(APIView):
    """
    List all cryptocurrencies
    """
    def get(self, request):
        cryptos = Cryptocurrency.objects.all()[:100]  # Limit to top 100
        data = []
        for crypto in cryptos:
            data.append({
                'id': str(crypto.id),
                'coingecko_id': crypto.coingecko_id,
                'name': crypto.name,
                'symbol': crypto.symbol,
                'current_price_usd': float(crypto.current_price_usd),
                'market_cap_rank': crypto.market_cap_rank,
                'price_change_percentage_24h': float(crypto.price_change_percentage_24h),
                'image_url': crypto.image_url,
            })
        return Response(data)


class CryptocurrencyDetailAPIView(APIView):
    """
    Get cryptocurrency details
    """
    def get(self, request, symbol):
        crypto = get_object_or_404(Cryptocurrency, symbol__iexact=symbol)
        data = {
            'id': str(crypto.id),
            'coingecko_id': crypto.coingecko_id,
            'name': crypto.name,
            'symbol': crypto.symbol,
            'current_price_usd': float(crypto.current_price_usd),
            'market_cap': crypto.market_cap,
            'total_volume': crypto.total_volume,
            'market_cap_rank': crypto.market_cap_rank,
            'price_change_24h': float(crypto.price_change_24h),
            'price_change_percentage_24h': float(crypto.price_change_percentage_24h),
            'price_change_percentage_7d': float(crypto.price_change_percentage_7d),
            'price_change_percentage_30d': float(crypto.price_change_percentage_30d),
            'image_url': crypto.image_url,
            'last_updated': crypto.last_updated.isoformat(),
        }
        return Response(data)


class PriceHistoryAPIView(APIView):
    """
    Get price history for a cryptocurrency
    """
    def get(self, request, symbol):
        crypto = get_object_or_404(Cryptocurrency, symbol__iexact=symbol)
        
        # Get time range from query params
        days = int(request.GET.get('days', 30))
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        price_history = PriceHistory.objects.filter(
            cryptocurrency=crypto,
            timestamp__gte=start_date,
            timestamp__lte=end_date
        ).order_by('timestamp')
        
        data = []
        for price in price_history:
            data.append({
                'timestamp': price.timestamp.isoformat(),
                'price_usd': float(price.price_usd),
                'market_cap': price.market_cap,
                'total_volume': price.total_volume,
            })
        
        return Response({
            'cryptocurrency': crypto.symbol,
            'period_days': days,
            'data': data
        })


class MarketDataAPIView(APIView):
    """
    Get latest market data
    """
    def get(self, request):
        try:
            market_data = MarketData.objects.latest('timestamp')
            data = {
                'total_market_cap_usd': market_data.total_market_cap_usd,
                'total_volume_24h_usd': market_data.total_volume_24h_usd,
                'bitcoin_dominance_percentage': float(market_data.bitcoin_dominance_percentage),
                'ethereum_dominance_percentage': float(market_data.ethereum_dominance_percentage),
                'fear_greed_index': market_data.fear_greed_index,
                'market_cap_change_24h': float(market_data.market_cap_change_24h),
                'timestamp': market_data.timestamp.isoformat(),
            }
            return Response(data)
        except MarketData.DoesNotExist:
            return Response({'error': 'No market data available'}, status=404)


class TrendingCryptosAPIView(APIView):
    """
    Get trending cryptocurrencies
    """
    def get(self, request):
        # Get top gainers in last 24h
        trending = Cryptocurrency.objects.filter(
            price_change_percentage_24h__gt=0
        ).order_by('-price_change_percentage_24h')[:10]
        
        data = []
        for crypto in trending:
            data.append({
                'name': crypto.name,
                'symbol': crypto.symbol,
                'current_price_usd': float(crypto.current_price_usd),
                'price_change_percentage_24h': float(crypto.price_change_percentage_24h),
                'market_cap_rank': crypto.market_cap_rank,
                'image_url': crypto.image_url,
            })
        
        return Response(data)


class NewsListAPIView(APIView):
    """
    Get latest crypto news
    """
    def get(self, request):
        news = NewsArticle.objects.all()[:20]
        data = []
        for article in news:
            data.append({
                'id': str(article.id),
                'title': article.title,
                'summary': article.summary,
                'source_name': article.source_name,
                'source_url': article.source_url,
                'author': article.author,
                'published_at': article.published_at.isoformat(),
                'image_url': article.image_url,
                'sentiment_score': float(article.sentiment_score) if article.sentiment_score else None,
            })
        return Response(data)


class CryptoNewsAPIView(APIView):
    """
    Get news for specific cryptocurrency
    """
    def get(self, request, symbol):
        crypto = get_object_or_404(Cryptocurrency, symbol__iexact=symbol)
        news = NewsArticle.objects.filter(
            related_cryptocurrencies=crypto
        )[:10]
        
        data = []
        for article in news:
            data.append({
                'id': str(article.id),
                'title': article.title,
                'summary': article.summary,
                'source_name': article.source_name,
                'source_url': article.source_url,
                'published_at': article.published_at.isoformat(),
                'sentiment_score': float(article.sentiment_score) if article.sentiment_score else None,
            })
        
        return Response({
            'cryptocurrency': crypto.symbol,
            'news': data
        })


class UpdatePricesAPIView(APIView):
    """
    Trigger price updates for all cryptocurrencies
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        # This would trigger the Celery task to update prices
        # For now, return a placeholder response
        return Response({'status': 'Price update triggered'})


class UpdateSinglePriceAPIView(APIView):
    """
    Trigger price update for single cryptocurrency
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request, symbol):
        crypto = get_object_or_404(Cryptocurrency, symbol__iexact=symbol)
        # This would trigger the Celery task to update single price
        # For now, return a placeholder response
        return Response({
            'status': f'Price update triggered for {crypto.symbol}',
            'cryptocurrency': crypto.symbol
        })


class CryptoSearchAPIView(APIView):
    """
    Search cryptocurrencies
    """
    def get(self, request):
        query = request.GET.get('q', '')
        if not query:
            return Response({'error': 'Query parameter required'}, status=400)
        
        cryptos = Cryptocurrency.objects.filter(
            name__icontains=query
        ) | Cryptocurrency.objects.filter(
            symbol__icontains=query
        )
        
        data = []
        for crypto in cryptos[:20]:
            data.append({
                'id': str(crypto.id),
                'name': crypto.name,
                'symbol': crypto.symbol,
                'current_price_usd': float(crypto.current_price_usd),
                'market_cap_rank': crypto.market_cap_rank,
                'image_url': crypto.image_url,
            })
        
        return Response({
            'query': query,
            'results': data
        })


class APIUsageStatsView(APIView):
    """
    Get API usage statistics
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        # Get stats for last 24 hours
        end_time = timezone.now()
        start_time = end_time - timedelta(hours=24)
        
        logs = APICallLog.objects.filter(
            created_at__gte=start_time,
            created_at__lte=end_time
        )
        
        stats = {
            'total_calls': logs.count(),
            'successful_calls': logs.filter(response_status__lt=400).count(),
            'failed_calls': logs.filter(response_status__gte=400).count(),
            'average_response_time': logs.aggregate(
                avg_time=models.Avg('response_time_ms')
            )['avg_time'] or 0,
        }
        
        # Group by provider
        provider_stats = {}
        for provider, _ in APICallLog.API_PROVIDERS:
            provider_logs = logs.filter(provider=provider)
            provider_stats[provider] = {
                'total_calls': provider_logs.count(),
                'successful_calls': provider_logs.filter(response_status__lt=400).count(),
                'failed_calls': provider_logs.filter(response_status__gte=400).count(),
            }
        
        return Response({
            'period': '24 hours',
            'overall_stats': stats,
            'provider_stats': provider_stats,
        })
